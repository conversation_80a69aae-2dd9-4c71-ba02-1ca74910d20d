from uuid import UUID

from fastapi import APIRouter, Body, Depends
from fastapi_injector import Injected
from starlette import status

from services.base.api.authentication.auth_guard import get_current_uuid
from services.data_service.api.constants import DataServicePrefixes, DocumentEndpointRoutes
from services.data_service.api.queries.document_query_api import DocumentQueryAPI
from services.data_service.application.use_cases.user_data.delete_user_data_use_case import DeleteUserDataUseCase

document_router = APIRouter(
    prefix=f"{DataServicePrefixes.V3}{DataServicePrefixes.DOCUMENT}",
    tags=["document"],
    responses={404: {"description": "Not found"}},
)


@document_router.delete(
    DocumentEndpointRoutes.BY_QUERY,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Delete user documents by query",
    description="""
    Schedules asynchronous deletion of user's V3 documents matching the specified query criteria.

    This endpoint schedules asynchronous deletion of the authenticated user's V3 documents
    that match the provided query criteria. The deletion is performed in the background,
    and the endpoint returns immediately with a 202 Accepted status.

    **Query Structure:**
    - **queries**: Array of typed queries, each targeting specific document types
    - **types**: Document types to target (UseCase, Plan, Template, Event, etc.)
    - **query**: Optional filter criteria using field-based queries and boolean operators

    **Supported Query Types:**
    - **exists**: Check if a field exists
    - **values**: Match specific field values
    - **pattern**: Text pattern matching
    - **range**: Numeric or date range filtering
    - **radius**: Geographic radius queries

    **Boolean Operators:**
    - **and**: All conditions must match
    - **or**: Any condition must match
    - **not**: Conditions must not match

    **Security:**
    - Only deletes documents owned by the authenticated user
    - User UUID is automatically added to all queries for security
    - Cannot delete documents from other users

    **Behavior:**
    - If query is null, deletes ALL user documents (equivalent to /all_data endpoint)
    - Returns immediately after scheduling the deletion task
    - Actual deletion happens asynchronously in the background
    - Assets are only deleted when deleting all user data (query=null)
    """,
    response_description="Deletion task scheduled successfully. Returns empty response with 202 status.",
    responses={
        202: {"description": "Deletion task scheduled successfully", "content": {"application/json": {"example": {}}}},
        400: {
            "description": "Bad request - invalid query structure or field names",
            "content": {
                "application/json": {
                    "examples": [
                        {
                            "summary": "Invalid query structure",
                            "value": {"detail": "Invalid query structure: missing required field 'types'"},
                        },
                        {
                            "summary": "Invalid field names",
                            "value": {"detail": "Invalid field name in query: unknown_field"},
                        },
                    ]
                }
            },
        },
        401: {"description": "Authentication required"},
        404: {"description": "User not found"},
        409: {
            "description": "Conflict - another deletion or upload operation is already in progress",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Another data operation is currently in progress. Please wait for it to complete."
                    }
                }
            },
        },
    },
)
async def delete_user_data_by_query_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    query: DocumentQueryAPI | None = Body(
        default=None,
        description="Query criteria to filter documents for deletion. If null, deletes all user documents.",
        examples=[
            {
                "summary": "Delete specific document types",
                "description": "Delete all use cases and plans for the user",
                "value": {"queries": [{"types": ["UseCase", "Plan"], "query": None}]},
            },
            {
                "summary": "Delete documents with field criteria",
                "description": "Delete archived use cases created before a specific date",
                "value": {
                    "queries": [
                        {
                            "types": ["UseCase"],
                            "query": {
                                "type": "and",
                                "queries": [
                                    {"type": "exists", "field_name": "archived_at"},
                                    {"type": "range", "field_name": "created_at", "lt": "2024-01-01T00:00:00Z"},
                                ],
                            },
                        }
                    ]
                },
            },
            {
                "summary": "Delete documents by tags",
                "description": "Delete all documents containing specific tags",
                "value": {
                    "queries": [
                        {
                            "types": ["UseCase", "Plan", "Template"],
                            "query": {"type": "values", "field_name": "tags", "values": ["deprecated", "old"]},
                        }
                    ]
                },
            },
        ],
    ),
    use_case: DeleteUserDataUseCase = Injected(DeleteUserDataUseCase),
):
    """
    Delete user documents matching specified query criteria.

    This endpoint schedules asynchronous deletion of the authenticated user's V3 documents
    that match the provided query criteria. The deletion is performed in the background,
    and the endpoint returns immediately with a 202 Accepted status.

    **Query Structure:**
    - **queries**: Array of typed queries, each targeting specific document types
    - **types**: Document types to target (UseCase, Plan, Template, Event, etc.)
    - **query**: Optional filter criteria using field-based queries and boolean operators

    **Supported Query Types:**
    - **exists**: Check if a field exists
    - **values**: Match specific field values
    - **pattern**: Text pattern matching
    - **range**: Numeric or date range filtering
    - **radius**: Geographic radius queries

    **Boolean Operators:**
    - **and**: All conditions must match
    - **or**: Any condition must match
    - **not**: Conditions must not match

    **Security:**
    - Only deletes documents owned by the authenticated user
    - User UUID is automatically added to all queries for security
    - Cannot delete documents from other users

    **Behavior:**
    - If query is null, deletes ALL user documents (equivalent to /all_data endpoint)
    - Returns immediately after scheduling the deletion task
    - Actual deletion happens asynchronously in the background
    - Assets are only deleted when deleting all user data (query=null)

    **Error Responses:**
    - **409 Conflict**: Another deletion or upload operation is already in progress
    - **400 Bad Request**: Invalid query structure or field names
    - **401 Unauthorized**: Authentication required
    - **404 Not Found**: User not found
    """
    return await use_case.execute_async(user_uuid=user_uuid, query=query.to_query() if query else None)


@document_router.delete(
    DocumentEndpointRoutes.ALL_DATA,
    status_code=status.HTTP_202_ACCEPTED,
    summary="Delete all user documents and assets",
    description="""
    Schedule complete deletion of all user's V3 documents and associated assets.

    This endpoint schedules comprehensive deletion of all the authenticated user's V3 data,
    including all document types (events, plans, use cases, templates, records) and associated
    file assets. This is the most complete data deletion operation available.

    **Key Features:**
    - **Complete Data Removal**: Deletes all user documents and associated files
    - **Asynchronous Processing**: Returns immediately while deletion happens in background
    - **Asset Cleanup**: Automatically removes all uploaded files and media
    - **Atomic Operation**: All data is deleted together or the operation fails
    - **GDPR Compliance**: Supports right to be forgotten requirements

    **What Gets Deleted:**
    - **All V3 Documents**: Events, Plans, Use Cases, Templates, and Records
    - **File Assets**: All uploaded files, images, audio, video, and documents
    - **Metadata**: All document metadata and system properties
    - **Relationships**: All document relationships and references
    - **User Preferences**: Associated user settings and configurations

    **Security and Safety:**
    - Only deletes data owned by the authenticated user
    - Cannot affect other users' data
    - Requires valid authentication token
    - Operation cannot be undone once started

    **Use Cases:**
    - Account closure and data purging
    - GDPR compliance (right to be forgotten)
    - Complete data reset for testing environments
    - Privacy-focused comprehensive data cleanup
    - Migration preparation for data export/import

    **Important Considerations:**
    - **Irreversible**: This operation cannot be undone
    - **Complete**: All user data will be permanently deleted
    - **Alternative**: Consider `/by_query` endpoint for selective deletion
    - **Timing**: Assets may take additional time to be fully removed from storage
    - **Background**: Deletion happens asynchronously after endpoint returns
    """,
    response_description="Deletion task scheduled successfully. Returns empty response with 202 status.",
    responses={
        202: {"description": "Deletion task scheduled successfully", "content": {"application/json": {"example": {}}}},
        401: {"description": "Authentication required"},
        404: {"description": "User not found"},
        409: {
            "description": "Conflict - another deletion or upload operation is already in progress",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Another data operation is currently in progress. Please wait for it to complete."
                    }
                }
            },
        },
    },
)
async def delete_all_user_data_endpoint(
    user_uuid: UUID = Depends(get_current_uuid),
    use_case: DeleteUserDataUseCase = Injected(DeleteUserDataUseCase),
):
    return await use_case.execute_async(user_uuid=user_uuid, query=None)
